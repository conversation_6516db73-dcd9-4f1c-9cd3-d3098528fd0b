/**
 * Utility functions for initializing Chart.js charts
 */

export const initArea<PERSON>hart = () => {
  if (typeof document !== 'undefined' && typeof window !== 'undefined' && window.Chart) {
    const ctx = document.getElementById('myAreaChart');
    if (ctx) {
      new window.Chart(ctx, {
        type: 'line',
        data: {
          labels: [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
          ],
          datasets: [{
            label: 'Sessions',
            lineTension: 0.3,
            backgroundColor: 'rgba(2,117,216,0.2)',
            borderColor: 'rgba(2,117,216,1)',
            pointRadius: 5,
            pointBackgroundColor: 'rgba(2,117,216,1)',
            pointBorderColor: 'rgba(255,255,255,0.8)',
            pointHoverRadius: 5,
            pointHoverBackgroundColor: 'rgba(2,117,216,1)',
            pointHitRadius: 50,
            pointBorderWidth: 2,
            data: [10000, 30162, 26263, 18394, 18287, 28682, 31274, 33259, 25849, 24159, 32651, 31984]
          }]
        },
        options: {
          scales: {
            xAxes: [{
              time: {
                unit: 'date'
              },
              gridLines: {
                display: false
              },
              ticks: {
                maxTicksLimit: 7
              }
            }],
            yAxes: [{
              ticks: {
                min: 0,
                max: 40000,
                maxTicksLimit: 5
              },
              gridLines: {
                color: 'rgba(0, 0, 0, .125)'
              }
            }]
          },
          legend: {
            display: false
          }
        }
      });
    }
  }
};

export const initBarChart = () => {
  if (typeof document !== 'undefined' && typeof window !== 'undefined' && window.Chart) {
    const ctx = document.getElementById('myBarChart');
    if (ctx) {
      new window.Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['January', 'February', 'March', 'April', 'May', 'June'],
          datasets: [{
            label: 'Revenue',
            backgroundColor: 'rgba(2,117,216,1)',
            borderColor: 'rgba(2,117,216,1)',
            data: [4215, 5312, 6251, 7841, 9821, 14984]
          }]
        },
        options: {
          scales: {
            xAxes: [{
              time: {
                unit: 'month'
              },
              gridLines: {
                display: false
              },
              ticks: {
                maxTicksLimit: 6
              }
            }],
            yAxes: [{
              ticks: {
                min: 0,
                max: 15000,
                maxTicksLimit: 5
              },
              gridLines: {
                display: true
              }
            }]
          },
          legend: {
            display: false
          }
        }
      });
    }
  }
};

export const initCharts = () => {
  initAreaChart();
  initBarChart();
};
