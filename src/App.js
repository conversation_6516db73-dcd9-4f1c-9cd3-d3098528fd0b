import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import Footer from './components/Footer';
import Login from './pages/Login';
import EstimateVehicle from './pages/EstimateVehicle';

function App() {
  const [sidebarToggled, setSidebarToggled] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarToggled(!sidebarToggled);
  };

  useEffect(() => {
    // Load Bootstrap JS for dropdowns, etc.
    const bootstrapScript = document.createElement('script');
    bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js';
    bootstrapScript.async = true;
    bootstrapScript.crossOrigin = 'anonymous';
    document.body.appendChild(bootstrapScript);

    // Load custom scripts
    const script = document.createElement('script');
    script.src = process.env.PUBLIC_URL + '/js/scripts.js';
    script.async = true;
    document.body.appendChild(script);

    // Load Chart.js
    const chartScript = document.createElement('script');
    chartScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js';
    chartScript.async = true;
    chartScript.crossOrigin = 'anonymous';
    document.body.appendChild(chartScript);

    // Load SimpleDatatables
    const dataTableScript = document.createElement('script');
    dataTableScript.src = 'https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js';
    dataTableScript.async = true;
    dataTableScript.crossOrigin = 'anonymous';
    document.body.appendChild(dataTableScript);

    return () => {
      // Cleanup if needed
      document.body.removeChild(script);
      document.body.removeChild(bootstrapScript);
      document.body.removeChild(chartScript);
      document.body.removeChild(dataTableScript);
    };
  }, []);

  // Estado para controlar si el usuario está autenticado
  const [isLoggedIn, setIsLoggedIn] = useState(() => {
    // Comprobar si hay un token guardado en localStorage al iniciar
    return localStorage.getItem('isLoggedIn') === 'true';
  });

  // Actualizar localStorage cuando cambie el estado de isLoggedIn
  useEffect(() => {
    localStorage.setItem('isLoggedIn', isLoggedIn);
  }, [isLoggedIn]);

      const handleLogout = () => {
    // Eliminar datos de sesión del localStorage
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userToken');
    // Actualizar el estado
    setIsLoggedIn(false);
      };

      return (
    isLoggedIn ? (
      <Router>
        <div className={`sb-nav-fixed ${sidebarToggled ? 'sb-sidenav-toggled' : ''}`}>
          <Navbar handleSidebarToggle={handleSidebarToggle} onLogout={handleLogout} />
          <div id="layoutSidenav">
            <Sidebar />
            <div id="layoutSidenav_content">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/estimate-vehicle" element={<EstimateVehicle />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
              <Footer />
            </div>
          </div>
        </div>
      </Router>
    ) : (
      <Login onLoginSuccess={() => setIsLoggedIn(true)} />
    )
  );
}

export default App;