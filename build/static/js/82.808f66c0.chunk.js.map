{"version": 3, "file": "static/js/82.808f66c0.chunk.js", "mappings": "6IAOO,MAYMA,EAAQC,eAAOC,EAAUC,GAAgC,IAAtBC,EAAQC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAEtD,IAd+BH,IAExBA,GAAYA,EAASM,OAAOF,OAAS,EAYvCG,CAAiBP,GACpB,MAAM,IAAIQ,MAAM,0CAGlB,IACE,MACMC,EAAQ,GAAAC,OADEC,uBACW,UAE3BC,QAAQC,IAAI,aAAcJ,GAE1B,MAAMK,QAAiBC,MAAMN,EAAU,CACrCO,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,OAAU,oBAEZC,KAAMC,KAAKC,UAAU,CACnBpB,WACAC,eAIJW,QAAQC,IAAI,YAAaC,GAGzB,MAAMO,EAAcP,EAASG,QAAQK,IAAI,gBAEzC,IAAKR,EAASS,GAAI,CAChB,IAAIC,EAAe,+BACfC,EAAmB,CAAC,EAGxB,GAAIJ,GAAeA,EAAYK,SAAS,oBACtC,IACE,MAAMC,QAAkBb,EAASc,OACjCJ,EAAeG,EAAUE,SAAWL,EAGhCG,EAAUG,SACZL,EAAmBE,EAAUG,OAEjC,CAAE,MAAOC,GACPnB,QAAQoB,MAAM,gCAAiCD,EACjD,MAGA,IACE,MAAME,QAAkBnB,EAASoB,OACjCtB,QAAQoB,MAAM,mBAAoBC,EACpC,CAAE,MAAOF,GACPnB,QAAQoB,MAAM,+BAAgCD,EAChD,CAIF,MAAMC,EAAQ,IAAIxB,MAAMgB,GAExB,MADAQ,EAAMP,iBAAmBA,EACnBO,CACR,CAGA,IAAKX,IAAgBA,EAAYK,SAAS,oBACxC,MAAM,IAAIlB,MAAM,qCAGlB,MAAM2B,QAAqBrB,EAASc,QAC9B,KAAEQ,EAAI,MAAEC,GAAUF,EAExB,IAAKE,EACH,MAAM,IAAI7B,MAAM,iDAYlB,OARIN,GACFoC,aAAaC,QAAQ,YAAaF,GAClCC,aAAaC,QAAQ,OAAQpB,KAAKC,UAAUgB,MAE5CI,eAAeD,QAAQ,YAAaF,GACpCG,eAAeD,QAAQ,OAAQpB,KAAKC,UAAUgB,KAGzCA,CACT,CAAE,MAAOJ,GAEP,MADApB,QAAQoB,MAAM,eAAgBA,GACxBA,CACR,CACF,C", "sources": ["utils/auth.js"], "sourcesContent": ["// Basic authentication utilities\n\n/**\n * Validates username format\n * @param {string} username - Username to validate\n * @returns {boolean} - Whether username is valid\n */\nexport const validateUsername = (username) => {\n  // Solo verificamos que el username no esté vacío\n  return username && username.trim().length > 0;\n};\n\n/**\n * Performs login with Laravel API\n * @param {string} username - Username\n * @param {string} password - User password\n * @param {boolean} remember - Whether to remember user\n * @returns {Promise} - Resolves with user data or rejects with error\n */\nexport const login = async (username, password, remember = false) => {\n  // Basic validation\n  if (!validateUsername(username)) {\n    throw new Error('Por favor ingrese un nombre de usuario');\n  }\n\n  try {\n    const baseUrl = process.env.REACT_APP_API_URL;\n    const loginUrl = `${baseUrl}/login`;\n\n    console.log('Login URL:', loginUrl);\n\n    const response = await fetch(loginUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({\n        username, // Usar username como está\n        password\n      })\n    });\n\n    console.log('Response:', response);\n\n    // Primero verificamos el tipo de contenido de la respuesta\n    const contentType = response.headers.get('content-type');\n\n    if (!response.ok) {\n      let errorMessage = 'Error en la autenticación';\n      let validationErrors = {};\n\n      // Solo intentamos parsear como JSON si el contenido es JSON\n      if (contentType && contentType.includes('application/json')) {\n        try {\n          const errorData = await response.json();\n          errorMessage = errorData.message || errorMessage;\n\n          // Capturar errores de validación si existen\n          if (errorData.errors) {\n            validationErrors = errorData.errors;\n          }\n        } catch (e) {\n          console.error('Error parsing error response:', e);\n        }\n      } else {\n        // Si no es JSON, intentamos obtener el texto\n        try {\n          const errorText = await response.text();\n          console.error('Server response:', errorText);\n        } catch (e) {\n          console.error('Error reading response text:', e);\n        }\n      }\n\n      // Lanzar un error con estructura para manejar mensajes de validación\n      const error = new Error(errorMessage);\n      error.validationErrors = validationErrors;\n      throw error;\n    }\n\n    // Verificamos que la respuesta sea JSON\n    if (!contentType || !contentType.includes('application/json')) {\n      throw new Error('Respuesta inesperada del servidor');\n    }\n\n    const responseData = await response.json();\n    const { data, token } = responseData;\n\n    if (!token) {\n      throw new Error('No se recibió un token de autenticación');\n    }\n\n    // Store auth token and user data\n    if (remember) {\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('user', JSON.stringify(data));\n    } else {\n      sessionStorage.setItem('authToken', token);\n      sessionStorage.setItem('user', JSON.stringify(data));\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Login error:', error);\n    throw error;\n  }\n};\n\n/**\n * Checks if user is authenticated\n * @returns {boolean} - Whether user is authenticated\n */\nexport const isAuthenticated = () => {\n  const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');\n  if (!token) return false;\n\n  // You could also validate token expiration here if your token contains exp claim\n  // and you're decoding it client-side\n\n  return true;\n};\n\n/**\n * Logs out the current user\n * @returns {Promise} - Resolves when logout is complete\n */\nexport const logout = async () => {\n  const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');\n\n  // Clear local storage and session storage\n  localStorage.removeItem('authToken');\n  localStorage.removeItem('user');\n  sessionStorage.removeItem('authToken');\n  sessionStorage.removeItem('user');\n\n  // Optionally notify the backend about logout (token invalidation)\n  if (token) {\n    try {\n      // Construir la URL correctamente para evitar doble slash\n      const baseUrl = process.env.REACT_APP_BACKEND_URL;\n      const logoutUrl = `${baseUrl}/logout`.replace(/([^:]\\/)\\/+/g, \"$1\");\n\n      await fetch(logoutUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n    } catch (error) {\n      console.error('Error during logout:', error);\n      // Continue with logout even if the API call fails\n    }\n  }\n\n  // Redirect to login page\n  window.location.href = '/login';\n};\n"], "names": ["login", "async", "username", "password", "remember", "arguments", "length", "undefined", "trim", "validateUsername", "Error", "loginUrl", "concat", "process", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "contentType", "get", "ok", "errorMessage", "validationErrors", "includes", "errorData", "json", "message", "errors", "e", "error", "errorText", "text", "responseData", "data", "token", "localStorage", "setItem", "sessionStorage"], "sourceRoot": ""}