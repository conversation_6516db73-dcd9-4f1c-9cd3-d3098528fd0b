"use strict";(self.webpackChunkit_sentinel_frontend=self.webpackChunkit_sentinel_frontend||[]).push([[82],{82:(e,o,r)=>{r.d(o,{login:()=>t});const t=async function(e,o){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!(e=>e&&e.trim().length>0)(e))throw new Error("Por favor ingrese un nombre de usuario");try{const n="".concat("http://localhost/api","/login");console.log("Login URL:",n);const s=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({username:e,password:o})});console.log("Response:",s);const a=s.headers.get("content-type");if(!s.ok){let e="Error en la autenticaci\xf3n",o={};if(a&&a.includes("application/json"))try{const r=await s.json();e=r.message||e,r.errors&&(o=r.errors)}catch(t){console.error("Error parsing error response:",t)}else try{const e=await s.text();console.error("Server response:",e)}catch(t){console.error("Error reading response text:",t)}const r=new Error(e);throw r.validationErrors=o,r}if(!a||!a.includes("application/json"))throw new Error("Respuesta inesperada del servidor");const i=await s.json(),{data:c,token:l}=i;if(!l)throw new Error("No se recibi\xf3 un token de autenticaci\xf3n");return r?(localStorage.setItem("authToken",l),localStorage.setItem("user",JSON.stringify(c))):(sessionStorage.setItem("authToken",l),sessionStorage.setItem("user",JSON.stringify(c))),c}catch(n){throw console.error("Login error:",n),n}}}}]);
//# sourceMappingURL=82.808f66c0.chunk.js.map