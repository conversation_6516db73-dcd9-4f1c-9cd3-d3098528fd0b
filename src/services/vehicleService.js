/**
 * Service for vehicle-related API calls
 */

/**
 * Fetches all vehicle makes from the API
 * @returns {Promise<Array>} - Promise resolving to an array of vehicle makes
 */
export const getAllVehicleMakes = async () => {
  try {
    const baseUrl = process.env.REACT_APP_API_URL;

    const response = await fetch(`${baseUrl}/vehicle-makes/all`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching vehicle makes:', error);
    throw error;
  }
};
