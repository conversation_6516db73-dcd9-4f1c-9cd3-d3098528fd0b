/**
 * Utility for making authenticated API requests
 */

/**
 * Get the authentication token from storage
 * @returns {string|null} - The auth token or null if not found
 */
export const getAuthToken = () => {
  return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || null;
};

/**
 * Make an authenticated API request
 * @param {string} endpoint - API endpoint to call (without base URL)
 * @param {Object} options - Fetch options
 * @returns {Promise} - The API response
 */
export const apiRequest = async (endpoint, options = {}) => {
  const token = getAuthToken();

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json'
    }
  };

  // Add authorization header if token exists
  if (token) {
    defaultOptions.headers['Authorization'] = `Bearer ${token}`;
  }

  // Merge options
  const fetchOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  const baseUrl = process.env.REACT_APP_BACKEND_URL;
  const url = `${baseUrl}${endpoint}`.replace(/([^:]\/)\/+/g, "$1");

  try {
    const response = await fetch(url, fetchOptions);

    // Handle unauthorized responses
    if (response.status === 401) {
      // Clear tokens and redirect to login
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      sessionStorage.removeItem('authToken');
      sessionStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Su sesión ha expirado. Por favor inicie sesión nuevamente.');
    }

    // Verificar el tipo de contenido
    const contentType = response.headers.get('content-type');

    if (!response.ok) {
      let errorMessage = 'Error en la solicitud';

      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
      } else {
        try {
          const errorText = await response.text();
          console.error('Server response:', errorText);
        } catch (e) {
          console.error('Error reading response text:', e);
        }
      }

      throw new Error(errorMessage);
    }

    // Para respuestas exitosas
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      // Si no es JSON, devolvemos el texto
      return {
        text: await response.text(),
        status: response.status
      };
    }
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};
