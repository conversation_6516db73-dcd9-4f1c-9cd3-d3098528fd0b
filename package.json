{"name": "it-sentinel-frontend", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-free": "^6.3.0", "bootstrap": "^5.2.3", "chart.js": "^2.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "simple-datatables": "^7.1.2"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "dotenv-webpack": "^8.1.0", "html-webpack-plugin": "^5.5.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}