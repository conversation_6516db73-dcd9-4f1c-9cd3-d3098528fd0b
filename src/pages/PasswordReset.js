import React, { useState } from 'react';
import AuthLayout from '../components/AuthLayout';

function PasswordReset() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess(false);
    setLoading(true);

    try {
      // Simulate password reset request (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess(true);
    } catch (err) {
      setError(err.message || 'Failed to send password reset email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="card shadow-lg border-0 rounded-lg mt-5">
        <div className="card-header"><h3 className="text-center font-weight-light my-4">Password Recovery</h3></div>
        <div className="card-body">
          {error && <div className="alert alert-danger">{error}</div>}
          {success && (
            <div className="alert alert-success">
              Password reset instructions have been sent to {email}. Please check your email.
            </div>
          )}
          <div className="small mb-3 text-muted">Enter your email address and we will send you a link to reset your password.</div>
          <form onSubmit={handleSubmit}>
            <div className="form-floating mb-3">
              <input 
                className="form-control" 
                id="inputEmail" 
                type="email" 
                placeholder="<EMAIL>" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <label htmlFor="inputEmail">Email address</label>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-4 mb-0">
              <a className="small" href="/login">Return to login</a>
              <button 
                type="submit" 
                className="btn btn-primary" 
                disabled={loading || success}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Processing...
                  </>
                ) : 'Reset Password'}
              </button>
            </div>
          </form>
        </div>
        <div className="card-footer text-center py-3">
          <div className="small"><a href="/register">Need an account? Sign up!</a></div>
        </div>
      </div>
    </AuthLayout>
  );
}

export default PasswordReset;
