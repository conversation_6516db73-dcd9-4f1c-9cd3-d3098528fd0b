import React, { useState } from 'react';
import AuthLayout from '../components/AuthLayout';

function RegisterPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);

    try {
      // Simulate registration (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Redirect to login after successful registration
      alert('Registration successful! Please log in.');
      window.location.href = '/login';
    } catch (err) {
      setError(err.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="card shadow-lg border-0 rounded-lg mt-5">
        <div className="card-header"><h3 className="text-center font-weight-light my-4">Create Account</h3></div>
        <div className="card-body">
          {error && <div className="alert alert-danger">{error}</div>}
          <form onSubmit={handleSubmit}>
            <div className="row mb-3">
              <div className="col-md-6">
                <div className="form-floating mb-3 mb-md-0">
                  <input 
                    className="form-control" 
                    id="inputFirstName" 
                    type="text" 
                    placeholder="Enter your first name" 
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="inputFirstName">First name</label>
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-floating">
                  <input 
                    className="form-control" 
                    id="inputLastName" 
                    type="text" 
                    placeholder="Enter your last name" 
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="inputLastName">Last name</label>
                </div>
              </div>
            </div>
            <div className="form-floating mb-3">
              <input 
                className="form-control" 
                id="inputEmail" 
                type="email" 
                placeholder="<EMAIL>" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
              <label htmlFor="inputEmail">Email address</label>
            </div>
            <div className="row mb-3">
              <div className="col-md-6">
                <div className="form-floating mb-3 mb-md-0">
                  <input 
                    className="form-control" 
                    id="inputPassword" 
                    type="password" 
                    placeholder="Create a password" 
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    minLength="6"
                  />
                  <label htmlFor="inputPassword">Password</label>
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-floating mb-3 mb-md-0">
                  <input 
                    className="form-control" 
                    id="inputPasswordConfirm" 
                    type="password" 
                    placeholder="Confirm password" 
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="inputPasswordConfirm">Confirm Password</label>
                </div>
              </div>
            </div>
            <div className="mt-4 mb-0">
              <div className="d-grid">
                <button 
                  type="submit" 
                  className="btn btn-primary btn-block" 
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Processing...
                    </>
                  ) : 'Create Account'}
                </button>
              </div>
            </div>
          </form>
        </div>
        <div className="card-footer text-center py-3">
          <div className="small"><a href="/login">Have an account? Go to login</a></div>
        </div>
      </div>
    </AuthLayout>
  );
}

export default RegisterPage;
