import React, { useState, useEffect, useRef } from 'react';
import { getAllVehicleMakes } from '../services/vehicleService';

function EstimateVehicle() {
  const [vehicleMakes, setVehicleMakes] = useState([]);
  const [selectedMake, setSelectedMake] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const fetchVehicleMakes = async () => {
      try {
        setIsLoading(true);
        const data = await getAllVehicleMakes();
        setVehicleMakes(data);
        setError(null);
      } catch (err) {
        setError('Failed to load vehicle makes. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVehicleMakes();
  }, []);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const filteredMakes = vehicleMakes.filter(make => 
    make.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMakeSelect = (make) => {
    setSelectedMake(make);
    setSearchTerm(make.name);
    setIsDropdownOpen(false);
  };
  return (
    <main>
      <div className="container-fluid px-4">
        <h1 className="mt-4">Vehicle Estimation</h1>
        <ol className="breadcrumb mb-4">
          <li className="breadcrumb-item"><a href="#!">Dashboard</a></li>
          <li className="breadcrumb-item active">Vehicle Estimation</li>
        </ol>
        <div className="card mb-4">
          <div className="card-header">
            <i className="fas fa-car me-1"></i>
            Vehicle Estimation Form
          </div>
          <div className="card-body">
            <form>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <input className="form-control" id="vehicleModel" type="text" placeholder="Enter vehicle model" />
                    <label htmlFor="vehicleModel">Vehicle Model</label>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <input className="form-control" id="vehicleYear" type="number" placeholder="Enter year" />
                    <label htmlFor="vehicleYear">Year</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3" ref={dropdownRef}>
                    <input 
                      className="form-control" 
                      id="vehicleMake" 
                      type="text" 
                      placeholder="Search vehicle make" 
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setIsDropdownOpen(true);
                      }}
                      onClick={() => setIsDropdownOpen(true)}
                    />
                    <label htmlFor="vehicleMake">Make</label>

                    {isDropdownOpen && (
                      <div className="dropdown-menu w-100 show position-absolute" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                        {isLoading ? (
                          <div className="dropdown-item">Loading...</div>
                        ) : error ? (
                          <div className="dropdown-item text-danger">{error}</div>
                        ) : filteredMakes.length > 0 ? (
                          filteredMakes.map(make => (
                            <button 
                              key={make.id} 
                              type="button" 
                              className="dropdown-item" 
                              onClick={() => handleMakeSelect(make)}
                            >
                              {make.name}
                            </button>
                          ))
                        ) : (
                          <div className="dropdown-item">No matches found</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <select className="form-select" id="vehicleCondition">
                      <option value="">Select condition</option>
                      <option value="excellent">Excellent</option>
                      <option value="good">Good</option>
                      <option value="fair">Fair</option>
                      <option value="poor">Poor</option>
                    </select>
                    <label htmlFor="vehicleCondition">Vehicle Condition</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <input className="form-control" id="mileage" type="number" placeholder="Enter mileage" />
                    <label htmlFor="mileage">Mileage</label>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <select className="form-select" id="transmission">
                      <option value="">Select transmission</option>
                      <option value="automatic">Automatic</option>
                      <option value="manual">Manual</option>
                    </select>
                    <label htmlFor="transmission">Transmission</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-12">
                  <div className="form-floating mb-3">
                    <textarea className="form-control" id="additionalNotes" style={{ height: '100px' }} placeholder="Enter additional notes"></textarea>
                    <label htmlFor="additionalNotes">Additional Notes</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-12">
                  <div className="form-check mb-3">
                    <input className="form-check-input" id="serviceHistory" type="checkbox" />
                    <label className="form-check-label" htmlFor="serviceHistory">Service History Available</label>
                  </div>
                </div>
              </div>
              {error && (
                <div className="alert alert-danger" role="alert">
                  {error}
                </div>
              )}
              <div className="d-flex align-items-center justify-content-between mt-4 mb-0">
                <button 
                  className="btn btn-secondary" 
                  type="reset" 
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedMake('');
                  }}
                >
                  Reset
                </button>
                <button 
                  className="btn btn-primary" 
                  type="button" 
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : 'Submit Estimation'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}

export default EstimateVehicle;
