import React, {useState} from 'react';
import AuthLayout from '../components/AuthLayout';

function Login({onLoginSuccess}) {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [rememberPassword, setRememberPassword] = useState(false);

    const [error, setError] = useState('');
    const [validationErrors, setValidationErrors] = useState({});
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        // Limpiar errores previos
        setError('');
        setValidationErrors({});
        setLoading(true);

        try {
            // Import dynamically to avoid circular dependencies
            const {login} = await import('../utils/auth');
            await login(username, password, rememberPassword);
            // Call the onLoginSuccess callback instead of redirecting
            if (onLoginSuccess) {
                onLoginSuccess();
            }
        } catch (err) {
            // Manejar los errores de validación si existen
            if (err.validationErrors) {
                setValidationErrors(err.validationErrors);
            }
            setError(err.message || 'Error al iniciar sesión. Por favor intente nuevamente.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <AuthLayout>
            <div className="card shadow-lg border-0 rounded-lg mt-5">
                <div className="card-header"><h3 className="text-center font-weight-light my-4">Login</h3></div>
                <div className="card-body">
                                         {error && !validationErrors.username && !validationErrors.email && !validationErrors.password && 
                     <div className="alert alert-danger">{error}</div>}
                    <form onSubmit={handleSubmit}>
                        <div className="form-floating mb-3">
                            <input
                                className={`form-control ${(validationErrors.username || validationErrors.email) ? 'is-invalid' : ''}`}
                                id="inputUsername"
                                type="text"
                                placeholder="Username"
                                value={username}
                                onChange={(e) => {
                                    setUsername(e.target.value);
                                    // Limpiar error cuando el usuario empieza a escribir
                                    if (validationErrors.username || validationErrors.email) {
                                        setValidationErrors(prev => ({ 
                                            ...prev, 
                                            username: null,
                                            email: null 
                                        }));
                                    }
                                }}
                                required
                            />
                            <label htmlFor="inputUsername">Username</label>
                            {(validationErrors.username || validationErrors.email) && (
                                <div className="invalid-feedback">
                                    {(validationErrors.username && validationErrors.username[0]) || 
                                     (validationErrors.email && validationErrors.email[0])}
                                </div>
                            )}
                        </div>
                        <div className="form-floating mb-3">
                            <input
                                className={`form-control ${validationErrors.password ? 'is-invalid' : ''}`}
                                id="inputPassword"
                                type="password"
                                placeholder="Password"
                                value={password}
                                onChange={(e) => {
                                    setPassword(e.target.value);
                                    // Limpiar error cuando el usuario empieza a escribir
                                    if (validationErrors.password) {
                                        setValidationErrors(prev => ({ ...prev, password: null }));
                                    }
                                }}
                                required
                            />
                            <label htmlFor="inputPassword">Password</label>
                            {validationErrors.password && (
                                <div className="invalid-feedback">
                                    {validationErrors.password[0]}
                                </div>
                            )}
                        </div>
                        <div className="form-check mb-3">
                            <input
                                className="form-check-input"
                                id="inputRememberPassword"
                                type="checkbox"
                                checked={rememberPassword}
                                onChange={(e) => setRememberPassword(e.target.checked)}
                            />
                            <label className="form-check-label" htmlFor="inputRememberPassword">Remember
                                Password</label>
                        </div>
                        <div className="d-flex align-items-center justify-content-between mt-4 mb-0">
                            <a className="small" href="#">Forgot Password?</a>
                            <button
                                type="submit"
                                className="btn btn-primary"
                                disabled={loading}
                            >
                                {loading ? (
                                    <>
                                        <span className="spinner-border spinner-border-sm me-2" role="status"
                                              aria-hidden="true"></span>
                                        Loading...
                                    </>
                                ) : 'Login'}
                            </button>
                        </div>
                    </form>
                </div>
                <div className="card-footer text-center py-3">
                    <div className="small"><a href="#">Need an account? Sign up!</a></div>
                </div>
            </div>
        </AuthLayout>
    );
}

export default Login;
