// Basic authentication utilities

/**
 * Validates username format
 * @param {string} username - Username to validate
 * @returns {boolean} - Whether username is valid
 */
export const validateUsername = (username) => {
  // Solo verificamos que el username no esté vacío
  return username && username.trim().length > 0;
};

/**
 * Performs login with Laravel API
 * @param {string} username - Username
 * @param {string} password - User password
 * @param {boolean} remember - Whether to remember user
 * @returns {Promise} - Resolves with user data or rejects with error
 */
export const login = async (username, password, remember = false) => {
  // Basic validation
  if (!validateUsername(username)) {
    throw new Error('Por favor ingrese un nombre de usuario');
  }

  try {
    const baseUrl = process.env.REACT_APP_API_URL;
    const loginUrl = `${baseUrl}/login`;

    console.log('Login URL:', loginUrl);

    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        username, // Usar username como está
        password
      })
    });

    console.log('Response:', response);

    // Primero verificamos el tipo de contenido de la respuesta
    const contentType = response.headers.get('content-type');

    if (!response.ok) {
      let errorMessage = 'Error en la autenticación';
      let validationErrors = {};

      // Solo intentamos parsear como JSON si el contenido es JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Capturar errores de validación si existen
          if (errorData.errors) {
            validationErrors = errorData.errors;
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
      } else {
        // Si no es JSON, intentamos obtener el texto
        try {
          const errorText = await response.text();
          console.error('Server response:', errorText);
        } catch (e) {
          console.error('Error reading response text:', e);
        }
      }

      // Lanzar un error con estructura para manejar mensajes de validación
      const error = new Error(errorMessage);
      error.validationErrors = validationErrors;
      throw error;
    }

    // Verificamos que la respuesta sea JSON
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error('Respuesta inesperada del servidor');
    }

    const responseData = await response.json();
    const { data, token } = responseData;

    if (!token) {
      throw new Error('No se recibió un token de autenticación');
    }

    // Store auth token and user data
    if (remember) {
      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(data));
    } else {
      sessionStorage.setItem('authToken', token);
      sessionStorage.setItem('user', JSON.stringify(data));
    }

    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

/**
 * Checks if user is authenticated
 * @returns {boolean} - Whether user is authenticated
 */
export const isAuthenticated = () => {
  const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
  if (!token) return false;

  // You could also validate token expiration here if your token contains exp claim
  // and you're decoding it client-side

  return true;
};

/**
 * Logs out the current user
 * @returns {Promise} - Resolves when logout is complete
 */
export const logout = async () => {
  const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

  // Clear local storage and session storage
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
  sessionStorage.removeItem('authToken');
  sessionStorage.removeItem('user');

  // Optionally notify the backend about logout (token invalidation)
  if (token) {
    try {
      // Construir la URL correctamente para evitar doble slash
      const baseUrl = process.env.REACT_APP_BACKEND_URL;
      const logoutUrl = `${baseUrl}/logout`.replace(/([^:]\/)\/+/g, "$1");

      await fetch(logoutUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Error during logout:', error);
      // Continue with logout even if the API call fails
    }
  }

  // Redirect to login page
  window.location.href = '/login';
};
